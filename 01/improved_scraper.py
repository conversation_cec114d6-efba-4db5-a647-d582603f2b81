#!/usr/bin/env python3
"""
Improved web scraper for infonet.fr that properly handles 202 status codes.
This script implements robust retry logic to wait for asynchronous processing to complete.
"""

import requests
from parsel import Selector
import csv
import time
import re

# Configuration
INPUT_FILE = "extrait_10001_20001_part_1.csv"
OUTPUT_FILE = "part-1-data-improved.csv"
RAW_NUMBER = 0
SKIP_RAW = 0  # Start from beginning

# Retry configuration
MAX_STATUS_URL_RETRIES = 30  # For status URL polling
MAX_ORIGINAL_URL_RETRIES = 15  # For original URL retries
STATUS_URL_WAIT_TIME = 2  # seconds between status URL retries
ORIGINAL_URL_WAIT_TIME = 3  # seconds between original URL retries
BASE_WAIT_TIME = 1  # seconds between requests

# Request headers and cookies
headers = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "en-US,en;q=0.9,sv;q=0.8,bn;q=0.7",
    "dnt": "1",
    "priority": "u=0, i",
    "referer": "https://infonet.fr/",
    "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Linux"',
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

cookies = {
    "infonet": "qqvb8m5ns14ouat1iqqojathvi",
    "trustedsite_visit": "1",
    "_ga": "GA1.1.558240977.1753774017",
    "axeptio_authorized_vendors": "%2CBing%2Cgoogle_analytics%2Cgoogle_ads%2Cbing%2CGoogle_Ads%2C",
    "axeptio_all_vendors": "%2CBing%2Cgoogle_analytics%2Cgoogle_ads%2Cbing%2CGoogle_Ads%2C",
    "axeptio_cookies": '{"$$token":"nrqh5thek3ottxm94yg0zk","$$date":"2025-07-29T07:27:17.991Z","$$cookiesVersion":{"name":"infonet website-fr","identifier":"6285f2013d207f8f42a5bad9"},"Bing":true,"google_analytics":true,"google_ads":true,"$$googleConsentMode":{"version":2,"analytics_storage":"granted","ad_storage":"granted","ad_user_data":"granted","ad_personalization":"granted"},"bing":true,"Google_Ads":true,"$$completed":true}',
    "aws-waf-token": "4c0c8d68-dde3-4484-ac2c-705f0f5e0b98:CgoAk+86ZA48AAAA:3PkjrHaYOl3gKph/uW3i6yUMA3yQWau4G/l4sIjnlG9X/fLHECPo6flMbOQACrZhTZ8am789abwHpPQJQJTKVnEw9cyb5CBYme9HbvVAAtNFqGVEs3x18WuGP61DWaIOzYhk44a3X3MWg8r1zBRexqVQz1tzrRxqs1Osr9vDse5VzKy9Q42rD2TCT09ZzlNJdWg=",
    "_gcl_au": "1.1.1240712368.1753774038.1952344250.1753777464.1753777656",
    "_uetsid": "750393806c4d11f0933d716a6db32fef|1mpn827|2|fy0|0|2036",
    "_ga_N4YEQJPVC9": "GS2.1.s1753808421$o6$g1$t1753808421$j60$l0$h878060388",
    "_uetvid": "750391306c4d11f0bbda75f5a60d5405|p2xfku|1753808422412|1|1|bat.bing.com/p/insights/c/j"
}

# Fallback for unidecode if not available
try:
    from unidecode import unidecode
except ImportError:
    def unidecode(txt):
        return txt.encode("ascii", "ignore").decode()


def build_infonet_url(siret: str, denomination: str) -> str:
    """
    Return the canonical Infonet URL for *siret* + *denominationUniteLegale*.
    
    1. Transliterate any accents to ASCII and lowercase the text.
    2. Replace every run of non‑alphanumeric characters with a single dash.
    3. Trim leading/trailing dashes.
    """
    slug = re.sub(r"[^a-z0-9]+", "-", unidecode(denomination).lower()).strip("-")
    return f"https://infonet.fr/entreprises/{siret}-{slug}/"


def handle_202_response(response, verif_url, headers, cookies):
    """
    Handle 202 status code responses with proper retry logic.
    Returns the final HTML content and updated response object.
    """
    print(f"Got 202 response for {verif_url}")
    
    # Try to get status URL from Location header first
    status_url = response.headers.get('Location')
    
    # If no Location header, try to parse JSON for StatusUrl
    if not status_url:
        try:
            json_data = response.json()
            status_url = json_data.get('StatusUrl')
        except (ValueError, requests.exceptions.JSONDecodeError):
            print(f"Warning: 202 response but no Location header and invalid JSON")
            print(f"Will retry original URL with longer waits...")
            status_url = None
    
    # If we have a status URL, poll it
    if status_url:
        print(f"Polling status URL: {status_url}")
        retry_count = 0
        while retry_count < MAX_STATUS_URL_RETRIES:
            time.sleep(STATUS_URL_WAIT_TIME)
            status_resp = requests.get(status_url, headers=headers, cookies=cookies)
            if status_resp.status_code == 200:
                print(f"✓ Status URL returned 200 after {retry_count + 1} retries")
                return status_resp.text, status_resp
            elif status_resp.status_code == 202:
                retry_count += 1
                print(f"Still processing... retry {retry_count}/{MAX_STATUS_URL_RETRIES}")
            else:
                print(f"Unexpected status code {status_resp.status_code} from status URL")
                break
        else:
            print(f"Max retries reached for status URL, using original response")
            return response.text, response
    else:
        # No status URL available, retry the original URL with longer waits
        print(f"No status URL, retrying original URL...")
        retry_count = 0
        while retry_count < MAX_ORIGINAL_URL_RETRIES:
            time.sleep(ORIGINAL_URL_WAIT_TIME)
            retry_resp = requests.get(verif_url, headers=headers, cookies=cookies)
            if retry_resp.status_code == 200:
                print(f"✓ Original URL returned 200 after {retry_count + 1} retries")
                return retry_resp.text, retry_resp
            elif retry_resp.status_code == 202:
                retry_count += 1
                print(f"Still 202... retry {retry_count}/{MAX_ORIGINAL_URL_RETRIES}")
            else:
                print(f"Got status code {retry_resp.status_code}, stopping retries")
                return retry_resp.text, retry_resp
        else:
            print(f"Max retries reached, still getting 202")
            return response.text, response
    
    return response.text, response


def extract_company_data(html):
    """Extract phone, email, and website from HTML content."""
    selector = Selector(html)
    phone = selector.xpath("//span[@id='header-company-phone']//a[starts-with(@href, 'tel:')]/text()").get()
    email = selector.xpath("//span[@id='header-company-email']//a[starts-with(@href, 'mailto:')]/text()").get()
    website = selector.xpath("//span[@id='header-company-website']//a/@href").get()
    return phone, email, website


def main():
    global RAW_NUMBER
    
    # Read the CSV file
    print(f"Reading data from {INPUT_FILE}...")
    data_rows = []
    with open(INPUT_FILE, 'r', encoding='utf-8') as file:
        csv_reader = csv.DictReader(file)
        original_fieldnames = csv_reader.fieldnames
        for row in csv_reader:
            data_rows.append(row)
    
    print(f"Loaded {len(data_rows)} rows")
    
    # Prepare CSV file with headers
    new_fieldnames = ['website', 'phone', 'email']
    all_fieldnames = list(original_fieldnames) + new_fieldnames
    
    # Create CSV file and write header
    with open(OUTPUT_FILE, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)
        writer.writeheader()
    
    print(f"Starting processing from row {SKIP_RAW}...")
    
    # Process each row
    processed_count = 0
    for index, row in enumerate(data_rows):
        if index < SKIP_RAW:
            continue
        
        siret = row['siret']
        denominationUniteLegale = row['denominationUniteLegale']
        verif_url = build_infonet_url(siret, denominationUniteLegale)
        
        print(f"\nProcessing #{RAW_NUMBER}: {siret} - {denominationUniteLegale[:50]}...")
        RAW_NUMBER += 1
        
        # Initialize default values
        website = None
        phone = None
        email = None
        
        try:
            # Make initial request
            response = requests.get(verif_url, headers=headers, cookies=cookies)
            time.sleep(BASE_WAIT_TIME)  # Basic rate limiting
            
            # Handle different response codes
            if response.status_code == 202:
                html, response = handle_202_response(response, verif_url, headers, cookies)
            elif response.status_code == 200:
                html = response.text
                print(f"✓ Got 200 response immediately")
            else:
                html = response.text
                print(f"Got status code: {response.status_code}")
            
            # Extract data
            phone, email, website = extract_company_data(html)
            
            print(f"  Phone: {phone}")
            print(f"  Email: {email}")
            print(f"  Website: {website}")
            print(f"  Final Status: {response.status_code}")
            
        except Exception as e:
            print(f"  ✗ Error processing {verif_url}: {str(e)}")
        
        # Prepare the result row
        result_row = row.copy()
        result_row.update({
            'website': website,
            'phone': phone,
            'email': email
        })
        
        # Save immediately to CSV file
        try:
            with open(OUTPUT_FILE, 'a', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)
                writer.writerow(result_row)
            processed_count += 1
        except Exception as e:
            print(f"  ✗ Error saving to CSV: {str(e)}")
    
    print(f"\nProcessing complete! Processed and saved {processed_count} records to {OUTPUT_FILE}.")


if __name__ == "__main__":
    main()
